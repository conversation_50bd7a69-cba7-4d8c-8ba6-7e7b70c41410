import { Tabs, TableProps } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import { useState } from 'react';
import { NWGConfig, TabsOrTableProps, NWGLib } from 'widgets/NewWorkGroupTabs';
import { customColumnFilters } from 'features/CustomColumnFilters';
import { DataGrid, TableRowData } from 'features/DataGrid';
import { apiUrls } from 'shared/api';
import { TableSelectionInfo } from 'shared/ui';
import { collapseWithLazyTable } from '../CollapseWithLazyTable';
import { collapseWithTable } from '../CollapseWithTable';
import { customRowRender } from '../CustomRowRender';

import styles from './styles.module.scss';

export const TabsOrTable: FC<TabsOrTableProps> = ({
  data,
  additionalButtons,
  getLazyTabContent,
  loadNestedTabs,
  handleSelect,
  nestedActiveKey,
  handleTab,
  nestedData,
  isPending,
  total,
  getData,
  currentPage,
  selectedTableKeys,
  isShowSelects,
  activeEndpoint,
  columnFilters,
  togglePopup,
  handleRowData,
  columnSorter,
  permissions,
  clearSelection,
}) => {
  const { tableConfig, tableConfigWithLazyTabs, customColumnsWidth } = NWGLib;
  const [filterDropdown, setFilterDropdown] = useState('');

  // ========================================================================
  // ВРЕМЕННАЯ ЛОГИКА СМЕШАННОГО РЕЖИМА ЗАГРУЗКИ ТАБОВ
  // ========================================================================
  //
  // ТЕКУЩЕЕ СОСТОЯНИЕ:
  // - packageDef: Полностью ленивая загрузка (каждый таб загружается отдельно при клике)
  // - request: Старая ленивая загрузка (все табы загружаются сразу при раскрытии строки)
  //
  // РАЗНИЦА МЕЖДУ РЕЖИМАМИ:
  // 1. Старый ленивый (request):
  //    - tableData приходит с сервера уже загруженным
  //    - Ленивость: данные загружаются при раскрытии строки, но все табы сразу
  //    - Использует collapseWithTable (простое отображение)
  //
  // 2. Полностью ленивый (packageDef):
  //    - tableData приходит как null
  //    - Ленивость: каждый таб загружается отдельно при клике на него
  //    - Использует collapseWithLazyTable + getLazyTabContent
  //
  // ДЛЯ МИГРАЦИИ request НА ПОЛНОСТЬЮ ЛЕНИВЫЙ РЕЖИМ:
  // 1. В useNestedData.ts: переместить 'krg3_request_notice' из legacyLazyEndpoints в fullyLazyEndpoints
  // 2. Раскомментировать логику queryParams.requestId в getLazyTabContent
  // 3. Убедиться, что бэкенд поддерживает загрузку отдельных табов для request
  // 4. Протестировать функциональность
  // 5. Удалить старую логику (isLegacyTabsEndpoint, collapseWithTable и связанный код)
  // 6. Упростить эту функцию до использования только полностью ленивого режима
  // ========================================================================

  // Эндпойнты с полностью ленивой загрузкой (каждый таб загружается отдельно при клике)
  const isFullyLazyEndpoint =
    activeEndpoint === apiUrls.workGroup.nestedTabsEndpoints.packageDef;

  // Эндпойнты со старой ленивой загрузкой (все табы загружаются сразу при раскрытии строки)
  // TODO: После тестирования перевести на полностью ленивый режим и удалить эту логику
  const isLegacyLazyEndpoint =
    activeEndpoint === apiUrls.workGroup.nestedTabsEndpoints.request;

  const getTableConfig = (): TableProps<TableRowData> => {
    // Полностью ленивая загрузка - каждый таб загружается при клике
    if (isFullyLazyEndpoint && getLazyTabContent) {
      return tableConfigWithLazyTabs(
        collapseWithLazyTable,
        customRowRender,
        () => getData(currentPage),
        togglePopup,
        handleRowData,
        activeEndpoint,
        permissions,
        getLazyTabContent,
        false, // isRequest = false для полностью ленивого режима
        Array.isArray(data) && nestedData
          ? nestedData?.rows || []
          : (data as TableColumnsAndRowsWithPagination)?.rows || [],
      );
    }

    // Старый ленивый режим - все табы загружаются сразу при раскрытии строки
    // TODO: Удалить после перевода всех эндпойнтов на полностью ленивый режим
    return tableConfig(
      collapseWithTable,
      customRowRender,
      () => getData(currentPage),
      togglePopup,
      handleRowData,
      activeEndpoint,
      permissions,
      isLegacyLazyEndpoint, // isRequest для совместимости
      Array.isArray(data) && nestedData
        ? nestedData?.rows || []
        : (data as TableColumnsAndRowsWithPagination)?.rows || [],
    );
  };

  return (
    <>
      {Array.isArray(data) && data.length !== 0 && (
        <Tabs
          items={data}
          activeKey={nestedActiveKey}
          onChange={(key) =>
            handleTab(key, data?.[Number(key) - 1]?.endpoint || '')
          }
          type="card"
        />
      )}

      <DataGrid
        key={activeEndpoint}
        hideColumnSearch
        hideSorter
        footerAdditionalComponent={
          <TableSelectionInfo selectedRowCount={selectedTableKeys.length} />
        }
        additionalClassNames={{
          container: styles.container,
          table: styles.containerTable,
        }}
        paginationProps={{
          show: true,
          pageSize: 10,
          onPaginatorClick: (page) => getData(page),
          currentPage,
          hideOnSinglePage: true,
          total,
          disabled: isPending,
          showSizeChanger: false,
        }}
        columnsProps={(column) => ({
          ...column,

          ...(column.sortable && {
            onLazyLoadSort: (newSortOrder) => {
              columnSorter.handleSort(
                newSortOrder
                  ? {
                      sort: column?.dataIndex ?? '',
                      direction: newSortOrder,
                    }
                  : null,
                Boolean(Array.isArray(data) && nestedData !== null),
              );
            },
            lazySortOrder:
              columnSorter.sortOrder?.sort === column.dataIndex
                ? columnSorter.sortOrder?.direction
                : undefined,
          }),

          ...(Object.hasOwn(column, 'filterType') &&
            column?.filterType !== null && {
              filters: [],
              filteredValue: [],
              onFilterDropdownOpenChange: (value) =>
                value
                  ? setFilterDropdown(column.dataIndex)
                  : setFilterDropdown(''),
              filterDropdownOpen: column.dataIndex === filterDropdown,
              filterDropdown: () =>
                customColumnFilters(
                  column,
                  (value) => {
                    setFilterDropdown('');
                    columnFilters.onSubmit(
                      value,
                      Array.isArray(data) ? 'nested' : 'main',
                    );
                    clearSelection();
                  },
                  columnFilters.filters,
                  (value) => {
                    setFilterDropdown('');
                    columnFilters.onReset(
                      value,
                      Array.isArray(data) ? 'nested' : 'main',
                    );
                    clearSelection();
                  },
                ),
            }),

          ...(column.columnType !== NWGConfig.constants.DEFAULT_TYPE && {
            width: customColumnsWidth(column?.columnType || ''),
            align: 'center',
            ...(column.columnType !== NWGConfig.constants.FEED_TYPE && {
              fixed: 'right',
            }),
          }),
          ...((NWGConfig.constants.UKEP_UNEP.includes(column.dataIndex) ||
            NWGConfig.constants.IS_MAIN.includes(column.dataIndex)) && {
            align: 'center',
          }),
          filtered: columnFilters.filters.some(
            (item) => item.column === column.dataIndex,
          ),
          render: (text, row) =>
            customRowRender(
              text,
              row,
              column,
              () => getData(currentPage),
              togglePopup,
              handleRowData,
              activeEndpoint,
              permissions,
              activeEndpoint === apiUrls.workGroup.nestedTabsEndpoints.request,
            ),
        })}
        tableAdditionProps={{
          rowClassName: (record) =>
            classNames({
              // TODO: вынести на косту или енам
              [styles.errorRow]: record.statusId === 'request_error',
            }),
          loading: isPending,
          ...getTableConfig(),
          ...(isShowSelects && {
            rowSelection: {
              preserveSelectedRowKeys: true,
              selectedRowKeys: selectedTableKeys,
              getCheckboxProps: (row) => ({
                disabled: Boolean(row.rowId?.isRead),
              }),
              onChange: (keys, rows) => handleSelect({ keys, rows }),
            },
          }),
        }}
        additionalButtons={additionalButtons.filter(
          (button) => button.title !== '',
        )}
        resizableProps={{ isActive: true }}
        columns={
          nestedData !== null && Array.isArray(data)
            ? nestedData?.columns || []
            : (data as TableColumnsAndRowsWithPagination)?.columns || []
        }
        rows={
          nestedData !== null && Array.isArray(data)
            ? nestedData.rows || []
            : (data as TableColumnsAndRowsWithPagination).rows || []
        }
        nestedTableProps={{
          tableAdditionProps: getTableConfig(),
        }}
        {...(loadNestedTabs && { nestedTableLoader: loadNestedTabs })}
      />
    </>
  );
};
