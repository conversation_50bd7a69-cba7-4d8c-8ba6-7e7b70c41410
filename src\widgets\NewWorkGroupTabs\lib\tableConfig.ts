import { TableProps } from 'antd';
import type { TableConfigProps } from 'widgets/NewWorkGroupTabs';
import { NestedTabsWithTable, TableRowData } from 'features/DataGrid';

export const tableConfig: TableConfigProps = (
  collapseWithTable,
  customRowRender,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  isRequest,
  rows?,
): TableProps<TableRowData> => ({
  size: 'middle',
  pagination: false,
  scroll: { x: '100%', y: '100%' },
  ...(rows &&
    rows.some(
      (row) => Object.hasOwn(row, 'nestedTable') || row.rowId?.hasNested,
    ) && {
      expandable: {
        rowExpandable: (row) =>
          Boolean(Object.hasOwn(row, 'nestedTable') || row.rowId?.hasNested),
        expandedRowRender: (row) => {
          // Если нет nestedTable, но есть hasNested - данные будут загружены через nestedTableLoader
          if (row.nestedTable) {
            return collapseWithTable(
              row.nestedTable as NestedTabsWithTable,
              customRowRender,
              row,
              refetch,
              togglePopup,
              handleRow,
              activeEndpoint,
              permissions,
              isRequest,
            );
          }
          // Для строк с hasNested: true без nestedTable возвращаем null
          // Состояние загрузки будет обрабатываться автоматически через nestedTableLoader
          return null;
        },
      },
    }),
});
