import { TableProps } from 'antd';
import { createElement, ReactNode } from 'react';
import type { TableConfigProps } from 'widgets/NewWorkGroupTabs';
import { NestedTabsWithTable, TableRowData } from 'features/DataGrid';
import { ApiContainer } from 'shared/ui';

// Функция для создания состояния загрузки nested таблицы
const createLoadingState = (row: TableRowData): ReactNode =>
  createElement(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ApiContainer as any,
    {
      isPending: row.nestedTableStatus?.isLoading || false,
      error: row.nestedTableStatus?.error || false,
    },
    createElement('div', { style: { minHeight: '100px' } }),
  );

export const tableConfig: TableConfigProps = (
  collapseWithTable,
  customRowRender,
  refetch,
  togglePopup,
  handleRow,
  activeEndpoint,
  permissions,
  isRequest,
  rows?,
): TableProps<TableRowData> => ({
  size: 'middle',
  pagination: false,
  scroll: { x: '100%', y: '100%' },
  ...(rows &&
    rows.some(
      (row) => Object.hasOwn(row, 'nestedTable') || row.rowId?.hasNested,
    ) && {
      expandable: {
        rowExpandable: (row) =>
          Boolean(Object.hasOwn(row, 'nestedTable') || row.rowId?.hasNested),
        expandedRowRender: (row) => {
          // Если нет nestedTable, но есть hasNested - данные будут загружены через nestedTableLoader
          if (row.nestedTable) {
            return collapseWithTable(
              row.nestedTable as NestedTabsWithTable,
              customRowRender,
              row,
              refetch,
              togglePopup,
              handleRow,
              activeEndpoint,
              permissions,
              isRequest,
            );
          }
          // Для строк с hasNested: true без nestedTable показываем состояние загрузки
          return createLoadingState(row);
        },
      },
    }),
});
